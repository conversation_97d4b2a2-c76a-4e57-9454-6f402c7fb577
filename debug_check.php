<?php

echo "开始调试检查...\n";

$servicesDir = 'php/api/app/Services/Api';
echo "检查目录: {$servicesDir}\n";

if (!is_dir($servicesDir)) {
    echo "目录不存在!\n";
    exit(1);
}

echo "目录存在\n";

// 获取文件列表
$files = glob($servicesDir . '/*.php');
echo "找到文件数量: " . count($files) . "\n";

if (count($files) > 0) {
    echo "前5个文件:\n";
    for ($i = 0; $i < min(5, count($files)); $i++) {
        echo "  " . basename($files[$i]) . "\n";
    }
}

// 测试读取第一个文件
if (count($files) > 0) {
    $testFile = $files[0];
    echo "\n测试读取文件: " . basename($testFile) . "\n";
    
    $content = file_get_contents($testFile);
    if ($content !== false) {
        echo "文件读取成功，大小: " . strlen($content) . " 字节\n";
        
        // 查找 public 方法
        preg_match_all('/public\s+function\s+(\w+)/', $content, $matches);
        echo "找到 public 方法数量: " . count($matches[1]) . "\n";
        
        if (count($matches[1]) > 0) {
            echo "方法列表: " . implode(', ', $matches[1]) . "\n";
        }
        
        // 检查是否包含目标字符串
        $hasServicesData = strpos($content, "'services_data' => \$services_data") !== false;
        echo "包含 'services_data' => \$services_data: " . ($hasServicesData ? "是" : "否") . "\n";
        
    } else {
        echo "文件读取失败\n";
    }
}

echo "\n调试完成\n";

?>
