{"scan_time": "2025-08-02 21:32:29", "total_files": 51, "total_methods": 136, "methods_with_services_data": 136, "methods_without_services_data": 0, "file_details": {"AdService.php": {"total_methods": 2, "with_services_data": 2, "without_services_data": 0, "methods_with": ["store", "update"], "methods_without": []}, "AiGenerationService.php": {"total_methods": 0, "with_services_data": 0, "without_services_data": 0, "methods_with": [], "methods_without": []}, "AiLoadBalancingService.php": {"total_methods": 0, "with_services_data": 0, "without_services_data": 0, "methods_with": [], "methods_without": []}, "AiModelService.php": {"total_methods": 0, "with_services_data": 0, "without_services_data": 0, "methods_with": [], "methods_without": []}, "AiPlatformFallbackService.php": {"total_methods": 0, "with_services_data": 0, "without_services_data": 0, "methods_with": [], "methods_without": []}, "AiPlatformHealthService.php": {"total_methods": 0, "with_services_data": 0, "without_services_data": 0, "methods_with": [], "methods_without": []}, "AiPlatformSelectionService.php": {"total_methods": 0, "with_services_data": 0, "without_services_data": 0, "methods_with": [], "methods_without": []}, "AiTaskService.php": {"total_methods": 8, "with_services_data": 8, "without_services_data": 0, "methods_with": ["createTask", "getTaskStatus", "getTaskList", "retryTask", "getUserTasks", "getTaskDetail", "getTaskStats", "getQueueStats"], "methods_without": []}, "AnalyticsService.php": {"total_methods": 6, "with_services_data": 6, "without_services_data": 0, "methods_with": ["getUserBehavior", "getSystemUsage", "getAiPerformance", "getRevenue", "getUserRetention", "generateCustomReport"], "methods_without": []}, "AssetService.php": {"total_methods": 4, "with_services_data": 4, "without_services_data": 0, "methods_with": ["getAssetList", "uploadAsset", "getAssetDetail", "deleteAsset"], "methods_without": []}, "AudioService.php": {"total_methods": 0, "with_services_data": 0, "without_services_data": 0, "methods_with": [], "methods_without": []}, "AuthService.php": {"total_methods": 4, "with_services_data": 4, "without_services_data": 0, "methods_with": ["register", "login", "loginByEmail", "getAuthUserID"], "methods_without": []}, "BatchService.php": {"total_methods": 9, "with_services_data": 9, "without_services_data": 0, "methods_with": ["createBatch", "createBatchTask", "getBatchStatus", "startBatch", "cancelBatch", "getBatchResults", "getBatchList", "retryFailedItems", "getBatchStats"], "methods_without": []}, "CacheService.php": {"total_methods": 17, "with_services_data": 17, "without_services_data": 0, "methods_with": ["set", "get", "delete", "setMultiple", "getMultiple", "flush", "healthCheck", "setWithTags", "flushTags", "clearCache", "warmupCache", "get<PERSON><PERSON><PERSON>", "getValue", "setValue", "deleteKeys", "getConfig", "getStats"], "methods_without": []}, "CharacterService.php": {"total_methods": 0, "with_services_data": 0, "without_services_data": 0, "methods_with": [], "methods_without": []}, "ConfigService.php": {"total_methods": 18, "with_services_data": 18, "without_services_data": 0, "methods_with": ["get", "set", "getAll", "getAppConfig", "getDatabaseConfig", "getCacheConfig", "getQueueConfig", "getLogConfig", "validateConfig", "getEnvVars", "clearCache", "getConfigs", "getPublicConfig", "updateConfig", "batchUpdateConfigs", "resetConfig", "getConfigHistory", "validateConfigValue"], "methods_without": []}, "DownloadManagementService.php": {"total_methods": 1, "with_services_data": 1, "without_services_data": 0, "methods_with": ["secureDownload"], "methods_without": []}, "FileService.php": {"total_methods": 0, "with_services_data": 0, "without_services_data": 0, "methods_with": [], "methods_without": []}, "ImageService.php": {"total_methods": 0, "with_services_data": 0, "without_services_data": 0, "methods_with": [], "methods_without": []}, "LogService.php": {"total_methods": 13, "with_services_data": 13, "without_services_data": 0, "methods_with": ["writeLog", "getLogs", "readLog", "analyzeLog", "cleanLogs", "downloadLog", "getLogConfig", "getSystemLogs", "getUserActionLogs", "getAiCallLogs", "getErrorLogs", "resolveError", "exportLogs"], "methods_without": []}, "ModelManagementService.php": {"total_methods": 0, "with_services_data": 0, "without_services_data": 0, "methods_with": [], "methods_without": []}, "ModelService.php": {"total_methods": 0, "with_services_data": 0, "without_services_data": 0, "methods_with": [], "methods_without": []}, "MusicService.php": {"total_methods": 0, "with_services_data": 0, "without_services_data": 0, "methods_with": [], "methods_without": []}, "NotificationService.php": {"total_methods": 14, "with_services_data": 14, "without_services_data": 0, "methods_with": ["sendNotification", "sendBulkNotifications", "getNotifications", "getNotification", "resendNotification", "getNotificationStats", "getTemplates", "createTemplate", "getNotificationConfig", "getUserNotifications", "mark<PERSON><PERSON><PERSON>", "markAllAsRead", "deleteNotification", "sendSystemNotification"], "methods_without": []}, "PermissionService.php": {"total_methods": 17, "with_services_data": 17, "without_services_data": 0, "methods_with": ["checkPermission", "checkMultiplePermissions", "getUserPermissions", "getUserRoles", "getRolePermissions", "assignRole", "removeRole", "getRoles", "getPermissions", "createRole", "updateRolePermissions", "getPermissionStats", "checkResourceAccess", "getUserPermissionDetails", "grantPermissions", "revokePermissions", "getPermissionHistory"], "methods_without": []}, "PointsService.php": {"total_methods": 0, "with_services_data": 0, "without_services_data": 0, "methods_with": [], "methods_without": []}, "PointsTransactionService.php": {"total_methods": 6, "with_services_data": 6, "without_services_data": 0, "methods_with": ["createFrozenTransaction", "handleSuccess", "handleFailure", "validateBalance", "getBalance", "getTransactions"], "methods_without": []}, "ProjectManagementService.php": {"total_methods": 0, "with_services_data": 0, "without_services_data": 0, "methods_with": [], "methods_without": []}, "ProjectService.php": {"total_methods": 0, "with_services_data": 0, "without_services_data": 0, "methods_with": [], "methods_without": []}, "PublicationService.php": {"total_methods": 0, "with_services_data": 0, "without_services_data": 0, "methods_with": [], "methods_without": []}, "RecommendationService.php": {"total_methods": 0, "with_services_data": 0, "without_services_data": 0, "methods_with": [], "methods_without": []}, "ResourceManagementService.php": {"total_methods": 0, "with_services_data": 0, "without_services_data": 0, "methods_with": [], "methods_without": []}, "ReviewService.php": {"total_methods": 0, "with_services_data": 0, "without_services_data": 0, "methods_with": [], "methods_without": []}, "SearchService.php": {"total_methods": 0, "with_services_data": 0, "without_services_data": 0, "methods_with": [], "methods_without": []}, "SocialService.php": {"total_methods": 0, "with_services_data": 0, "without_services_data": 0, "methods_with": [], "methods_without": []}, "SoundService.php": {"total_methods": 0, "with_services_data": 0, "without_services_data": 0, "methods_with": [], "methods_without": []}, "StoryService.php": {"total_methods": 0, "with_services_data": 0, "without_services_data": 0, "methods_with": [], "methods_without": []}, "StyleService.php": {"total_methods": 0, "with_services_data": 0, "without_services_data": 0, "methods_with": [], "methods_without": []}, "TaskManagementService.php": {"total_methods": 0, "with_services_data": 0, "without_services_data": 0, "methods_with": [], "methods_without": []}, "TemplateService.php": {"total_methods": 0, "with_services_data": 0, "without_services_data": 0, "methods_with": [], "methods_without": []}, "UserGrowthService.php": {"total_methods": 0, "with_services_data": 0, "without_services_data": 0, "methods_with": [], "methods_without": []}, "UserService.php": {"total_methods": 0, "with_services_data": 0, "without_services_data": 0, "methods_with": [], "methods_without": []}, "VersionControlService.php": {"total_methods": 0, "with_services_data": 0, "without_services_data": 0, "methods_with": [], "methods_without": []}, "VideoService.php": {"total_methods": 0, "with_services_data": 0, "without_services_data": 0, "methods_with": [], "methods_without": []}, "VoiceService.php": {"total_methods": 0, "with_services_data": 0, "without_services_data": 0, "methods_with": [], "methods_without": []}, "WebSocketEventService.php": {"total_methods": 0, "with_services_data": 0, "without_services_data": 0, "methods_with": [], "methods_without": []}, "WebSocketService.php": {"total_methods": 0, "with_services_data": 0, "without_services_data": 0, "methods_with": [], "methods_without": []}, "WebSocketTokenService.php": {"total_methods": 0, "with_services_data": 0, "without_services_data": 0, "methods_with": [], "methods_without": []}, "WorkPublishPermissionService.php": {"total_methods": 0, "with_services_data": 0, "without_services_data": 0, "methods_with": [], "methods_without": []}, "WorkPublishService.php": {"total_methods": 0, "with_services_data": 0, "without_services_data": 0, "methods_with": [], "methods_without": []}, "WorkflowService.php": {"total_methods": 17, "with_services_data": 17, "without_services_data": 0, "methods_with": ["createWorkflow", "executeWorkflow", "getExecutionStatus", "getWorkflows", "getWorkflow", "updateWorkflow", "deleteWorkflow", "pauseWorkflow", "resumeWorkflow", "getExecutionHistory", "getWorkflowStats", "getWorkflowDetail", "executeWorkflowWithData", "getExecutionStatusWithAuth", "provideStepInput", "cancelExecution", "getExecutionHistoryWithFilters"], "methods_without": []}}, "missing_methods": []}