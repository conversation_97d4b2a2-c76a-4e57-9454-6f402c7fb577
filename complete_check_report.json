{"scan_time": "2025-08-02 21:45:02", "total_files": 51, "total_methods": 353, "methods_with_services_data": 343, "methods_without_services_data": 10, "compliance_rate": 97.17, "file_details": {"AdService.php": {"total_methods": 2, "with_services_data": 2, "without_services_data": 0, "methods_with": ["store", "update"], "methods_without": []}, "AiGenerationService.php": {"total_methods": 4, "with_services_data": 4, "without_services_data": 0, "methods_with": ["generateText", "getTaskStatus", "getUserTasks", "retryTask"], "methods_without": []}, "AiLoadBalancingService.php": {"total_methods": 1, "with_services_data": 1, "without_services_data": 0, "methods_with": ["distributeTasks"], "methods_without": []}, "AiModelService.php": {"total_methods": 5, "with_services_data": 5, "without_services_data": 0, "methods_with": ["switchUserModel", "getPerformanceMetrics", "checkServiceHealth", "getUserDefaultModel", "getModelByPlatform"], "methods_without": []}, "AiPlatformFallbackService.php": {"total_methods": 2, "with_services_data": 2, "without_services_data": 0, "methods_with": ["<PERSON><PERSON><PERSON>back", "<PERSON><PERSON><PERSON><PERSON>"], "methods_without": []}, "AiPlatformHealthService.php": {"total_methods": 8, "with_services_data": 8, "without_services_data": 0, "methods_with": ["checkPlatformHealth", "checkAllPlatformsHealth", "getPlatformAvailability", "recordPerformanceMetric", "getHealthSummary", "checkAlertConditions", "getPlatformComparison", "getBusinessPlatforms"], "methods_without": []}, "AiPlatformSelectionService.php": {"total_methods": 4, "with_services_data": 4, "without_services_data": 0, "methods_with": ["selectOptimalPlatform", "validateBusinessType", "getSupportedPlatforms", "isPlatformSupported"], "methods_without": []}, "AiTaskService.php": {"total_methods": 12, "with_services_data": 12, "without_services_data": 0, "methods_with": ["createTask", "getTaskStatus", "cancelTask", "getTaskList", "retryTask", "getBatchTaskStatus", "getTaskRecoveryStatus", "getTimeoutConfig", "getUserTasks", "getTaskDetail", "getTaskStats", "getQueueStats"], "methods_without": []}, "AnalyticsService.php": {"total_methods": 6, "with_services_data": 6, "without_services_data": 0, "methods_with": ["getUserBehavior", "getSystemUsage", "getAiPerformance", "getRevenue", "getUserRetention", "generateCustomReport"], "methods_without": []}, "AssetService.php": {"total_methods": 4, "with_services_data": 4, "without_services_data": 0, "methods_with": ["getAssetList", "uploadAsset", "getAssetDetail", "deleteAsset"], "methods_without": []}, "AudioService.php": {"total_methods": 4, "with_services_data": 4, "without_services_data": 0, "methods_with": ["mixAudio", "getAudioMixStatus", "enhanceAudio", "getAudioEnhanceStatus"], "methods_without": []}, "AuthService.php": {"total_methods": 8, "with_services_data": 8, "without_services_data": 0, "methods_with": ["register", "login", "loginByEmail", "getAuthUserID", "logout", "forgotPassword", "resetPassword", "refreshToken"], "methods_without": []}, "BatchService.php": {"total_methods": 11, "with_services_data": 11, "without_services_data": 0, "methods_with": ["createBatch", "createBatchTask", "getBatchStatus", "startBatch", "cancelBatch", "getBatchResults", "getBatchList", "retryFailedItems", "getBatchStats", "generateResources", "getResourcesStatus"], "methods_without": []}, "CacheService.php": {"total_methods": 17, "with_services_data": 17, "without_services_data": 0, "methods_with": ["set", "get", "delete", "setMultiple", "getMultiple", "flush", "healthCheck", "setWithTags", "flushTags", "clearCache", "warmupCache", "get<PERSON><PERSON><PERSON>", "getValue", "setValue", "deleteKeys", "getConfig", "getStats"], "methods_without": []}, "CharacterService.php": {"total_methods": 9, "with_services_data": 9, "without_services_data": 0, "methods_with": ["getCategories", "bindCharacter", "unbindCharacter", "updateBinding", "getUserBindings", "getCharacters", "getCharacterDetail", "getRecommendations", "generateCharacter"], "methods_without": []}, "ConfigService.php": {"total_methods": 18, "with_services_data": 18, "without_services_data": 0, "methods_with": ["get", "set", "getAll", "getAppConfig", "getDatabaseConfig", "getCacheConfig", "getQueueConfig", "getLogConfig", "validateConfig", "getEnvVars", "clearCache", "getConfigs", "getPublicConfig", "updateConfig", "batchUpdateConfigs", "resetConfig", "getConfigHistory", "validateConfigValue"], "methods_without": []}, "DownloadManagementService.php": {"total_methods": 7, "with_services_data": 7, "without_services_data": 0, "methods_with": ["getDownloadHistory", "retryDownload", "getDownloadStatistics", "createDownloadLink", "secureDownload", "createBatchDownload", "cleanupDownloads"], "methods_without": []}, "FileService.php": {"total_methods": 5, "with_services_data": 5, "without_services_data": 0, "methods_with": ["uploadFile", "getUserFiles", "getFileDetail", "deleteFile", "getDownloadUrl"], "methods_without": []}, "ImageService.php": {"total_methods": 4, "with_services_data": 4, "without_services_data": 0, "methods_with": ["generateImage", "getImageStatus", "getImageResult", "batchGenerateImages"], "methods_without": []}, "LogService.php": {"total_methods": 13, "with_services_data": 13, "without_services_data": 0, "methods_with": ["writeLog", "getLogs", "readLog", "analyzeLog", "cleanLogs", "downloadLog", "getLogConfig", "getSystemLogs", "getUserActionLogs", "getAiCallLogs", "getErrorLogs", "resolveError", "exportLogs"], "methods_without": []}, "ModelManagementService.php": {"total_methods": 7, "with_services_data": 7, "without_services_data": 0, "methods_with": ["getAvailableModels", "getModelDetail", "testModel", "getUserUsageStats", "manageFavorite", "getUserFavorites", "switchUserModel"], "methods_without": []}, "ModelService.php": {"total_methods": 6, "with_services_data": 6, "without_services_data": 0, "methods_with": ["getAvailableModels", "getModelDetails", "invokeModel", "getModelUsageStats", "updateModelConfig", "getUserModelUsage"], "methods_without": []}, "MusicService.php": {"total_methods": 4, "with_services_data": 4, "without_services_data": 0, "methods_with": ["generateMusic", "getMusicStatus", "getMusicResult", "batchGenerateMusic"], "methods_without": []}, "NotificationService.php": {"total_methods": 14, "with_services_data": 14, "without_services_data": 0, "methods_with": ["sendNotification", "sendBulkNotifications", "getNotifications", "getNotification", "resendNotification", "getNotificationStats", "getTemplates", "createTemplate", "getNotificationConfig", "getUserNotifications", "mark<PERSON><PERSON><PERSON>", "markAllAsRead", "deleteNotification", "sendSystemNotification"], "methods_without": []}, "PermissionService.php": {"total_methods": 17, "with_services_data": 17, "without_services_data": 0, "methods_with": ["checkPermission", "checkMultiplePermissions", "getUserPermissions", "getUserRoles", "getRolePermissions", "assignRole", "removeRole", "getRoles", "getPermissions", "createRole", "updateRolePermissions", "getPermissionStats", "checkResourceAccess", "getUserPermissionDetails", "grantPermissions", "revokePermissions", "getPermissionHistory"], "methods_without": []}, "PointsService.php": {"total_methods": 9, "with_services_data": 9, "without_services_data": 0, "methods_with": ["recharge", "freezePoints", "releasePoints", "consumePoints", "handleTimeoutTransactions", "checkPoints", "confirmPointsUsage", "refundPoints", "refundPointsByFreezeId"], "methods_without": []}, "PointsTransactionService.php": {"total_methods": 6, "with_services_data": 6, "without_services_data": 0, "methods_with": ["createFrozenTransaction", "handleSuccess", "handleFailure", "validateBalance", "getBalance", "getTransactions"], "methods_without": []}, "ProjectManagementService.php": {"total_methods": 6, "with_services_data": 6, "without_services_data": 0, "methods_with": ["createTask", "manageCollaboration", "getProjectProgress", "assignResources", "getProjectStatistics", "getProjectMilestones"], "methods_without": []}, "ProjectService.php": {"total_methods": 10, "with_services_data": 10, "without_services_data": 0, "methods_with": ["checkAntiSpam", "createWithStory", "confirmTitle", "updateStatus", "createSimpleProject", "updateProject", "deleteProject", "getUserProjects", "getProjectDetail", "getProjectList"], "methods_without": []}, "PublicationService.php": {"total_methods": 7, "with_services_data": 7, "without_services_data": 0, "methods_with": ["publishWork", "getPublicationStatus", "updatePublication", "unpublishWork", "getUserPublications", "getPublicationPlaza", "getPublicationDetail"], "methods_without": []}, "RecommendationService.php": {"total_methods": 8, "with_services_data": 8, "without_services_data": 0, "methods_with": ["getContentRecommendations", "getUserRecommendations", "getTopicRecommendations", "submitFeedback", "getUserPreferences", "updateUserPreferences", "getRecommendationAnalytics", "getPersonalizedRecommendations"], "methods_without": []}, "ResourceManagementService.php": {"total_methods": 4, "with_services_data": 4, "without_services_data": 0, "methods_with": ["createGenerationTask", "getResourceStatus", "getResourceList", "deleteResource"], "methods_without": []}, "ReviewService.php": {"total_methods": 7, "with_services_data": 6, "without_services_data": 1, "methods_with": ["submitReview", "getReviewStatus", "submitAppeal", "getUserReviews", "getQueueStatus", "perform<PERSON>reCheck"], "methods_without": ["getReviewGuidelines"]}, "SearchService.php": {"total_methods": 2, "with_services_data": 2, "without_services_data": 0, "methods_with": ["globalSearch", "getSearchSuggestions"], "methods_without": []}, "SocialService.php": {"total_methods": 9, "with_services_data": 9, "without_services_data": 0, "methods_with": ["manageFollow", "getFollowList", "manageLike", "createComment", "getComments", "shareContent", "getSocialFeed", "getNotifications", "markNotificationsRead"], "methods_without": []}, "SoundService.php": {"total_methods": 4, "with_services_data": 4, "without_services_data": 0, "methods_with": ["generateSound", "getSoundStatus", "getSoundResult", "batchGenerateSounds"], "methods_without": []}, "StoryService.php": {"total_methods": 2, "with_services_data": 2, "without_services_data": 0, "methods_with": ["generateStory", "getStoryStatus"], "methods_without": []}, "StyleService.php": {"total_methods": 5, "with_services_data": 5, "without_services_data": 0, "methods_with": ["createStyle", "updateRating", "getRecommendedStyles", "searchStyles", "getCategoryStats"], "methods_without": []}, "TaskManagementService.php": {"total_methods": 6, "with_services_data": 6, "without_services_data": 0, "methods_with": ["cancelTask", "retryTask", "getBatchTaskStatus", "getBatchTaskStatusByBatchId", "getTimeoutConfig", "getRecoveryStatus"], "methods_without": []}, "TemplateService.php": {"total_methods": 7, "with_services_data": 7, "without_services_data": 0, "methods_with": ["createTemplate", "useTemplate", "getTemplateMarketplace", "getUserTemplates", "getTemplateDetail", "updateTemplate", "deleteTemplate"], "methods_without": []}, "UserGrowthService.php": {"total_methods": 10, "with_services_data": 10, "without_services_data": 0, "methods_with": ["getUserGrowthProfile", "getLeaderboard", "completeAchievement", "getDailyTasks", "completeDailyTask", "getGrowthHistory", "getGrowthStatistics", "setUserGoals", "getGrowthRecommendations", "getUserMilestones"], "methods_without": []}, "UserService.php": {"total_methods": 4, "with_services_data": 4, "without_services_data": 0, "methods_with": ["updatePreferences", "getUserStats", "updateProfile", "resetPreferences"], "methods_without": []}, "VersionControlService.php": {"total_methods": 6, "with_services_data": 6, "without_services_data": 0, "methods_with": ["createVersion", "getVersionHistory", "getVersionDetail", "setCurrentVersion", "deleteVersion", "compareVersions"], "methods_without": []}, "VideoService.php": {"total_methods": 3, "with_services_data": 3, "without_services_data": 0, "methods_with": ["generateVideo", "getVideoStatus", "getVideoResult"], "methods_without": []}, "VoiceService.php": {"total_methods": 7, "with_services_data": 7, "without_services_data": 0, "methods_with": ["synthesizeVoice", "getVoiceStatus", "batchSynthesizeVoices", "cloneVoice", "getVoiceCloneStatus", "customVoice", "getVoiceCustomStatus"], "methods_without": []}, "WebSocketEventService.php": {"total_methods": 8, "with_services_data": 8, "without_services_data": 0, "methods_with": ["pushAiGenerationProgress", "pushAiGenerationCompleted", "pushAiGenerationFailed", "pushPointsChanged", "pushCustomEvent", "pushSystemNotification", "pushToMultipleUsers", "pushBroadcast"], "methods_without": []}, "WebSocketService.php": {"total_methods": 8, "with_services_data": 3, "without_services_data": 5, "methods_with": ["authenticateConnection", "getUserSessions", "disconnectSession"], "methods_without": ["getServerStatus", "pushMessage", "pushToUser", "cleanupTimeoutSessions", "generateAuthToken"]}, "WebSocketTokenService.php": {"total_methods": 0, "with_services_data": 0, "without_services_data": 0, "methods_with": [], "methods_without": []}, "WorkPublishPermissionService.php": {"total_methods": 3, "with_services_data": 1, "without_services_data": 2, "methods_with": ["checkPublishPermission"], "methods_without": ["checkResourcePublishPermission", "mapModuleTypeToWorkType"]}, "WorkPublishService.php": {"total_methods": 3, "with_services_data": 1, "without_services_data": 2, "methods_with": ["publishWork"], "methods_without": ["getMyWorks", "getGallery"]}, "WorkflowService.php": {"total_methods": 17, "with_services_data": 17, "without_services_data": 0, "methods_with": ["createWorkflow", "executeWorkflow", "getExecutionStatus", "getWorkflows", "getWorkflow", "updateWorkflow", "deleteWorkflow", "pauseWorkflow", "resumeWorkflow", "getExecutionHistory", "getWorkflowStats", "getWorkflowDetail", "executeWorkflowWithData", "getExecutionStatusWithAuth", "provideStepInput", "cancelExecution", "getExecutionHistoryWithFilters"], "methods_without": []}}, "missing_methods": [{"file": "ReviewService.php", "method": "getReviewGuidelines"}, {"file": "WebSocketService.php", "method": "getServerStatus"}, {"file": "WebSocketService.php", "method": "pushMessage"}, {"file": "WebSocketService.php", "method": "pushToUser"}, {"file": "WebSocketService.php", "method": "cleanupTimeoutSessions"}, {"file": "WebSocketService.php", "method": "generateAuthToken"}, {"file": "WorkPublishPermissionService.php", "method": "checkResourcePublishPermission"}, {"file": "WorkPublishPermissionService.php", "method": "mapModuleTypeToWorkType"}, {"file": "WorkPublishService.php", "method": "getMyWorks"}, {"file": "WorkPublishService.php", "method": "getGallery"}]}