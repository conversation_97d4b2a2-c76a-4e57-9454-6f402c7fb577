<?php

/**
 * 批量检查 Services/Api 目录中所有服务文件的 public 方法
 * 查找是否存在 "'services_data' => $services_data" 字符串
 */

class ServicesDataChecker
{
    private $servicesDir;
    private $results = [];
    private $missingServicesData = [];

    public function __construct()
    {
        $this->servicesDir = 'php/api/app/Services/Api';
    }

    /**
     * 扫描所有服务文件
     */
    public function scanAllServices()
    {
        if (!is_dir($this->servicesDir)) {
            echo "目录不存在: {$this->servicesDir}\n";
            return;
        }

        $files = glob($this->servicesDir . '/*.php');
        
        echo "找到 " . count($files) . " 个服务文件\n";
        echo "开始扫描...\n\n";

        foreach ($files as $file) {
            $this->analyzeServiceFile($file);
        }

        $this->generateReport();
    }

    /**
     * 分析单个服务文件
     */
    private function analyzeServiceFile($filePath)
    {
        $fileName = basename($filePath);
        echo "正在分析: {$fileName}\n";

        $content = file_get_contents($filePath);
        if ($content === false) {
            echo "  错误: 无法读取文件\n";
            return;
        }

        // 使用更精确的正则表达式查找所有 public 方法
        // 这个正则表达式会匹配完整的方法体，包括嵌套的大括号
        $pattern = '/public\s+function\s+(\w+)\s*\([^)]*\)\s*\{/';
        preg_match_all($pattern, $content, $methodMatches, PREG_OFFSET_CAPTURE);

        $publicMethods = [];
        $methodsWithServicesData = [];
        $methodsWithoutServicesData = [];
        $constructorMethods = [];
        $nonConstructorMethods = [];

        foreach ($methodMatches[1] as $index => $methodMatch) {
            $methodName = $methodMatch[0];
            $methodStartPos = $methodMatches[0][$index][1];

            // 找到方法体的完整内容
            $methodBody = $this->extractMethodBody($content, $methodStartPos);

            $publicMethods[] = $methodName;

            // 区分构造函数和非构造函数
            if ($methodName === '__construct') {
                $constructorMethods[] = $methodName;
            } else {
                $nonConstructorMethods[] = $methodName;
            }

            // 检查方法体中是否包含 "'services_data' => $services_data"
            if (strpos($methodBody, "'services_data' => \$services_data") !== false) {
                $methodsWithServicesData[] = $methodName;
            } else {
                $methodsWithoutServicesData[] = $methodName;
                $this->missingServicesData[] = [
                    'file' => $fileName,
                    'method' => $methodName,
                    'is_constructor' => ($methodName === '__construct')
                ];
            }
        }

        $this->results[$fileName] = [
            'total_public_methods' => count($publicMethods),
            'constructor_methods' => $constructorMethods,
            'non_constructor_methods' => $nonConstructorMethods,
            'methods_with_services_data' => $methodsWithServicesData,
            'methods_without_services_data' => $methodsWithoutServicesData,
            'all_public_methods' => $publicMethods
        ];

        echo "  找到 " . count($publicMethods) . " 个 public 方法\n";
        echo "  其中构造函数: " . count($constructorMethods) . " 个\n";
        echo "  其中非构造函数: " . count($nonConstructorMethods) . " 个\n";
        echo "  包含 services_data: " . count($methodsWithServicesData) . " 个\n";
        echo "  缺少 services_data: " . count($methodsWithoutServicesData) . " 个\n\n";
    }

    /**
     * 提取方法体内容
     */
    private function extractMethodBody($content, $startPos)
    {
        $braceCount = 0;
        $inMethod = false;
        $methodBody = '';
        $length = strlen($content);

        for ($i = $startPos; $i < $length; $i++) {
            $char = $content[$i];

            if ($char === '{') {
                $braceCount++;
                $inMethod = true;
            } elseif ($char === '}') {
                $braceCount--;
            }

            if ($inMethod) {
                $methodBody .= $char;
            }

            if ($inMethod && $braceCount === 0) {
                break;
            }
        }

        return $methodBody;
    }

    /**
     * 生成检查报告
     */
    private function generateReport()
    {
        echo "=== 检查报告 ===\n\n";

        $totalFiles = count($this->results);
        $totalMethods = 0;
        $totalWithServicesData = 0;
        $totalWithoutServicesData = 0;

        foreach ($this->results as $fileName => $data) {
            $totalMethods += $data['total_public_methods'];
            $totalWithServicesData += count($data['methods_with_services_data']);
            $totalWithoutServicesData += count($data['methods_without_services_data']);
        }

        echo "总计:\n";
        echo "- 服务文件数量: {$totalFiles}\n";
        echo "- 总 public 方法数: {$totalMethods}\n";
        echo "- 包含 services_data 的方法: {$totalWithServicesData}\n";
        echo "- 缺少 services_data 的方法: {$totalWithoutServicesData}\n\n";

        if (!empty($this->missingServicesData)) {
            echo "=== 缺少 'services_data' => \$services_data 的方法列表 ===\n\n";

            // 分别显示构造函数和非构造函数
            $constructorMissing = array_filter($this->missingServicesData, function($item) {
                return $item['is_constructor'];
            });

            $nonConstructorMissing = array_filter($this->missingServicesData, function($item) {
                return !$item['is_constructor'];
            });

            if (!empty($constructorMissing)) {
                echo "构造函数 (__construct) 缺少 services_data:\n";
                $currentFile = '';
                foreach ($constructorMissing as $item) {
                    if ($currentFile !== $item['file']) {
                        $currentFile = $item['file'];
                        echo "  文件: {$currentFile}\n";
                    }
                    echo "    - {$item['method']}()\n";
                }
                echo "\n";
            }

            if (!empty($nonConstructorMissing)) {
                echo "非构造函数缺少 services_data:\n";
                $currentFile = '';
                foreach ($nonConstructorMissing as $item) {
                    if ($currentFile !== $item['file']) {
                        $currentFile = $item['file'];
                        echo "  文件: {$currentFile}\n";
                    }
                    echo "    - {$item['method']}()\n";
                }
                echo "\n";
            }
        }

        // 生成详细报告文件
        $this->generateDetailedReport();
    }

    /**
     * 生成详细的 JSON 报告文件
     */
    private function generateDetailedReport()
    {
        $reportData = [
            'scan_time' => date('Y-m-d H:i:s'),
            'summary' => [
                'total_files' => count($this->results),
                'total_methods' => array_sum(array_column($this->results, 'total_public_methods')),
                'methods_with_services_data' => array_sum(array_map(function($data) {
                    return count($data['methods_with_services_data']);
                }, $this->results)),
                'methods_without_services_data' => count($this->missingServicesData)
            ],
            'detailed_results' => $this->results,
            'missing_services_data' => $this->missingServicesData
        ];

        $reportFile = 'services_data_check_report.json';
        file_put_contents($reportFile, json_encode($reportData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        
        echo "详细报告已保存到: {$reportFile}\n";

        // 生成 CSV 格式的缺失列表
        $this->generateCsvReport();
    }

    /**
     * 生成 CSV 格式的缺失方法列表
     */
    private function generateCsvReport()
    {
        $csvFile = 'missing_services_data.csv';
        $handle = fopen($csvFile, 'w');

        // 写入 CSV 头部
        fputcsv($handle, ['服务文件名', '方法名', '是否构造函数'], ',', '"');

        // 写入数据
        foreach ($this->missingServicesData as $item) {
            fputcsv($handle, [
                $item['file'],
                $item['method'],
                $item['is_constructor'] ? '是' : '否'
            ], ',', '"');
        }

        fclose($handle);
        echo "缺失方法列表已保存到: {$csvFile}\n";
    }
}

// 执行检查
$checker = new ServicesDataChecker();
$checker->scanAllServices();

echo "\n检查完成！\n";
