<?php

echo "=== 完整检查 Services/Api 目录中的所有服务文件 ===\n";
echo "检查每个 public 方法是否包含 'services_data' => \$services_data\n\n";

$servicesDir = 'php/api/app/Services/Api';
$missingMethods = [];
$allResults = [];

// 获取所有 PHP 文件
$files = glob($servicesDir . '/*.php');
$totalFiles = count($files);

echo "找到 {$totalFiles} 个服务文件\n";
echo "开始逐个检查...\n\n";

$fileIndex = 0;
foreach ($files as $file) {
    $fileIndex++;
    $fileName = basename($file);
    echo "[{$fileIndex}/{$totalFiles}] 检查文件: {$fileName}\n";
    
    $content = file_get_contents($file);
    if ($content === false) {
        echo "  ❌ 错误: 无法读取文件\n\n";
        continue;
    }
    
    // 查找所有 public 方法
    preg_match_all('/public\s+function\s+(\w+)\s*\([^)]*\)\s*\{/', $content, $matches, PREG_OFFSET_CAPTURE);
    
    $publicMethods = [];
    $methodsWithServicesData = [];
    $methodsWithoutServicesData = [];
    
    foreach ($matches[1] as $index => $match) {
        $methodName = $match[0];
        $methodStartPos = $matches[0][$index][1];
        
        // 跳过构造函数
        if ($methodName === '__construct') {
            continue;
        }
        
        $publicMethods[] = $methodName;
        
        // 提取方法体
        $methodBody = extractMethodBody($content, $methodStartPos);
        
        // 检查是否包含 'services_data' => $services_data
        if (strpos($methodBody, "'services_data' => \$services_data") !== false) {
            $methodsWithServicesData[] = $methodName;
            echo "  ✅ {$methodName}\n";
        } else {
            $methodsWithoutServicesData[] = $methodName;
            $missingMethods[] = [
                'file' => $fileName,
                'method' => $methodName
            ];
            echo "  ❌ {$methodName} - 缺少 'services_data' => \$services_data\n";
        }
    }
    
    $allResults[$fileName] = [
        'total_methods' => count($publicMethods),
        'with_services_data' => count($methodsWithServicesData),
        'without_services_data' => count($methodsWithoutServicesData),
        'methods_with' => $methodsWithServicesData,
        'methods_without' => $methodsWithoutServicesData
    ];
    
    echo "  统计: {$allResults[$fileName]['total_methods']} 个方法，";
    echo "{$allResults[$fileName]['with_services_data']} 个包含，";
    echo "{$allResults[$fileName]['without_services_data']} 个缺少\n\n";
}

// 输出汇总结果
echo "=== 检查结果汇总 ===\n\n";

$totalMethods = array_sum(array_column($allResults, 'total_methods'));
$totalWithServicesData = array_sum(array_column($allResults, 'with_services_data'));
$totalWithoutServicesData = array_sum(array_column($allResults, 'without_services_data'));

echo "📊 统计数据:\n";
echo "- 服务文件总数: {$totalFiles}\n";
echo "- Public 方法总数: {$totalMethods} 个（不含构造函数）\n";
echo "- 包含 'services_data' => \$services_data: {$totalWithServicesData} 个\n";
echo "- 缺少 'services_data' => \$services_data: {$totalWithoutServicesData} 个\n\n";

if (empty($missingMethods)) {
    echo "🎉 所有 public 方法都包含 'services_data' => \$services_data\n";
} else {
    echo "⚠️  以下方法缺少 'services_data' => \$services_data:\n\n";
    
    $currentFile = '';
    foreach ($missingMethods as $missing) {
        if ($currentFile !== $missing['file']) {
            $currentFile = $missing['file'];
            echo "📁 {$currentFile}:\n";
        }
        echo "  - {$missing['method']}()\n";
    }
    
    echo "\n❌ 总计缺少的方法数量: " . count($missingMethods) . "\n";
    
    // 保存详细结果到 CSV
    $csvContent = "服务文件名,方法名\n";
    foreach ($missingMethods as $missing) {
        $csvContent .= "\"{$missing['file']}\",\"{$missing['method']}\"\n";
    }
    file_put_contents('missing_services_data_methods.csv', $csvContent);
    echo "📄 缺失方法列表已保存到: missing_services_data_methods.csv\n";
}

// 保存完整结果到 JSON
$reportData = [
    'scan_time' => date('Y-m-d H:i:s'),
    'total_files' => $totalFiles,
    'total_methods' => $totalMethods,
    'methods_with_services_data' => $totalWithServicesData,
    'methods_without_services_data' => $totalWithoutServicesData,
    'file_details' => $allResults,
    'missing_methods' => $missingMethods
];

file_put_contents('complete_services_check_report.json', json_encode($reportData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
echo "📄 完整报告已保存到: complete_services_check_report.json\n";

echo "\n✅ 检查完成！\n";

/**
 * 提取方法体内容
 */
function extractMethodBody($content, $startPos)
{
    $braceCount = 0;
    $inMethod = false;
    $methodBody = '';
    $length = strlen($content);
    
    for ($i = $startPos; $i < $length; $i++) {
        $char = $content[$i];
        
        if ($char === '{') {
            $braceCount++;
            $inMethod = true;
        } elseif ($char === '}') {
            $braceCount--;
        }
        
        if ($inMethod) {
            $methodBody .= $char;
        }
        
        if ($inMethod && $braceCount === 0) {
            break;
        }
    }
    
    return $methodBody;
}

?>
