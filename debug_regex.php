<?php

$testFile = 'php/api/app/Services/Api/AiGenerationService.php';
echo "测试文件: {$testFile}\n";

$content = file_get_contents($testFile);
echo "文件大小: " . strlen($content) . " 字节\n";

// 测试不同的正则表达式
echo "\n=== 测试正则表达式 ===\n";

// 原始正则
$pattern1 = '/public\s+function\s+(\w+)\s*\([^)]*\)\s*\{/';
preg_match_all($pattern1, $content, $matches1);
echo "正则1 找到: " . count($matches1[1]) . " 个方法\n";
if (count($matches1[1]) > 0) {
    echo "方法: " . implode(', ', $matches1[1]) . "\n";
}

// 简化正则
$pattern2 = '/public\s+function\s+(\w+)/';
preg_match_all($pattern2, $content, $matches2);
echo "正则2 找到: " . count($matches2[1]) . " 个方法\n";
if (count($matches2[1]) > 0) {
    echo "方法: " . implode(', ', $matches2[1]) . "\n";
}

// 更宽松的正则
$pattern3 = '/public\s+function\s+(\w+)\s*\(/';
preg_match_all($pattern3, $content, $matches3);
echo "正则3 找到: " . count($matches3[1]) . " 个方法\n";
if (count($matches3[1]) > 0) {
    echo "方法: " . implode(', ', $matches3[1]) . "\n";
}

// 手动查找一些文本
echo "\n=== 手动搜索 ===\n";
$pos = strpos($content, 'public function generateText');
echo "找到 'public function generateText': " . ($pos !== false ? "是 (位置: {$pos})" : "否") . "\n";

$pos = strpos($content, 'public function getTaskStatus');
echo "找到 'public function getTaskStatus': " . ($pos !== false ? "是 (位置: {$pos})" : "否") . "\n";

// 显示文件的前几行包含 public function 的内容
echo "\n=== 包含 'public function' 的行 ===\n";
$lines = explode("\n", $content);
foreach ($lines as $lineNum => $line) {
    if (strpos($line, 'public function') !== false) {
        echo "第" . ($lineNum + 1) . "行: " . trim($line) . "\n";
    }
}

?>
