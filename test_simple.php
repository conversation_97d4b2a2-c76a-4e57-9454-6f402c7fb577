<?php

echo "PHP 脚本开始运行...\n";

$servicesDir = 'php/api/app/Services/Api';

echo "检查目录: {$servicesDir}\n";

if (is_dir($servicesDir)) {
    echo "目录存在\n";
    $files = glob($servicesDir . '/*.php');
    echo "找到 " . count($files) . " 个文件\n";
    
    // 只检查前几个文件作为测试
    $testFiles = array_slice($files, 0, 3);
    
    foreach ($testFiles as $file) {
        echo "处理文件: " . basename($file) . "\n";
        
        $content = file_get_contents($file);
        if ($content !== false) {
            echo "  文件大小: " . strlen($content) . " 字节\n";
            
            // 查找 public 方法
            preg_match_all('/public\s+function\s+(\w+)/', $content, $matches);
            echo "  找到 " . count($matches[1]) . " 个 public 方法: " . implode(', ', $matches[1]) . "\n";
            
            // 检查是否包含目标字符串
            $hasServicesData = strpos($content, "'services_data' => \$services_data") !== false;
            echo "  包含 'services_data' => \$services_data: " . ($hasServicesData ? "是" : "否") . "\n";
        } else {
            echo "  无法读取文件\n";
        }
        echo "\n";
    }
} else {
    echo "目录不存在\n";
}

echo "脚本执行完成\n";

?>
