{"scan_time": "2025-08-02 21:20:20", "summary": {"total_files": 51, "total_methods": 154, "methods_with_services_data": 136, "methods_without_services_data": 18}, "detailed_results": {"AdService.php": {"total_public_methods": 2, "constructor_methods": [], "non_constructor_methods": ["store", "update"], "methods_with_services_data": ["store", "update"], "methods_without_services_data": [], "all_public_methods": ["store", "update"]}, "AiGenerationService.php": {"total_public_methods": 1, "constructor_methods": ["__construct"], "non_constructor_methods": [], "methods_with_services_data": [], "methods_without_services_data": ["__construct"], "all_public_methods": ["__construct"]}, "AiLoadBalancingService.php": {"total_public_methods": 1, "constructor_methods": ["__construct"], "non_constructor_methods": [], "methods_with_services_data": [], "methods_without_services_data": ["__construct"], "all_public_methods": ["__construct"]}, "AiModelService.php": {"total_public_methods": 0, "constructor_methods": [], "non_constructor_methods": [], "methods_with_services_data": [], "methods_without_services_data": [], "all_public_methods": []}, "AiPlatformFallbackService.php": {"total_public_methods": 1, "constructor_methods": ["__construct"], "non_constructor_methods": [], "methods_with_services_data": [], "methods_without_services_data": ["__construct"], "all_public_methods": ["__construct"]}, "AiPlatformHealthService.php": {"total_public_methods": 0, "constructor_methods": [], "non_constructor_methods": [], "methods_with_services_data": [], "methods_without_services_data": [], "all_public_methods": []}, "AiPlatformSelectionService.php": {"total_public_methods": 0, "constructor_methods": [], "non_constructor_methods": [], "methods_with_services_data": [], "methods_without_services_data": [], "all_public_methods": []}, "AiTaskService.php": {"total_public_methods": 9, "constructor_methods": ["__construct"], "non_constructor_methods": ["createTask", "getTaskStatus", "getTaskList", "retryTask", "getUserTasks", "getTaskDetail", "getTaskStats", "getQueueStats"], "methods_with_services_data": ["createTask", "getTaskStatus", "getTaskList", "retryTask", "getUserTasks", "getTaskDetail", "getTaskStats", "getQueueStats"], "methods_without_services_data": ["__construct"], "all_public_methods": ["__construct", "createTask", "getTaskStatus", "getTaskList", "retryTask", "getUserTasks", "getTaskDetail", "getTaskStats", "getQueueStats"]}, "AnalyticsService.php": {"total_public_methods": 6, "constructor_methods": [], "non_constructor_methods": ["getUserBehavior", "getSystemUsage", "getAiPerformance", "getRevenue", "getUserRetention", "generateCustomReport"], "methods_with_services_data": ["getUserBehavior", "getSystemUsage", "getAiPerformance", "getRevenue", "getUserRetention", "generateCustomReport"], "methods_without_services_data": [], "all_public_methods": ["getUserBehavior", "getSystemUsage", "getAiPerformance", "getRevenue", "getUserRetention", "generateCustomReport"]}, "AssetService.php": {"total_public_methods": 4, "constructor_methods": [], "non_constructor_methods": ["getAssetList", "uploadAsset", "getAssetDetail", "deleteAsset"], "methods_with_services_data": ["getAssetList", "uploadAsset", "getAssetDetail", "deleteAsset"], "methods_without_services_data": [], "all_public_methods": ["getAssetList", "uploadAsset", "getAssetDetail", "deleteAsset"]}, "AudioService.php": {"total_public_methods": 1, "constructor_methods": ["__construct"], "non_constructor_methods": [], "methods_with_services_data": [], "methods_without_services_data": ["__construct"], "all_public_methods": ["__construct"]}, "AuthService.php": {"total_public_methods": 4, "constructor_methods": [], "non_constructor_methods": ["register", "login", "loginByEmail", "getAuthUserID"], "methods_with_services_data": ["register", "login", "loginByEmail", "getAuthUserID"], "methods_without_services_data": [], "all_public_methods": ["register", "login", "loginByEmail", "getAuthUserID"]}, "BatchService.php": {"total_public_methods": 9, "constructor_methods": [], "non_constructor_methods": ["createBatch", "createBatchTask", "getBatchStatus", "startBatch", "cancelBatch", "getBatchResults", "getBatchList", "retryFailedItems", "getBatchStats"], "methods_with_services_data": ["createBatch", "createBatchTask", "getBatchStatus", "startBatch", "cancelBatch", "getBatchResults", "getBatchList", "retryFailedItems", "getBatchStats"], "methods_without_services_data": [], "all_public_methods": ["createBatch", "createBatchTask", "getBatchStatus", "startBatch", "cancelBatch", "getBatchResults", "getBatchList", "retryFailedItems", "getBatchStats"]}, "CacheService.php": {"total_public_methods": 17, "constructor_methods": [], "non_constructor_methods": ["set", "get", "delete", "setMultiple", "getMultiple", "flush", "healthCheck", "setWithTags", "flushTags", "clearCache", "warmupCache", "get<PERSON><PERSON><PERSON>", "getValue", "setValue", "deleteKeys", "getConfig", "getStats"], "methods_with_services_data": ["set", "get", "delete", "setMultiple", "getMultiple", "flush", "healthCheck", "setWithTags", "flushTags", "clearCache", "warmupCache", "get<PERSON><PERSON><PERSON>", "getValue", "setValue", "deleteKeys", "getConfig", "getStats"], "methods_without_services_data": [], "all_public_methods": ["set", "get", "delete", "setMultiple", "getMultiple", "flush", "healthCheck", "setWithTags", "flushTags", "clearCache", "warmupCache", "get<PERSON><PERSON><PERSON>", "getValue", "setValue", "deleteKeys", "getConfig", "getStats"]}, "CharacterService.php": {"total_public_methods": 1, "constructor_methods": ["__construct"], "non_constructor_methods": [], "methods_with_services_data": [], "methods_without_services_data": ["__construct"], "all_public_methods": ["__construct"]}, "ConfigService.php": {"total_public_methods": 18, "constructor_methods": [], "non_constructor_methods": ["get", "set", "getAll", "getAppConfig", "getDatabaseConfig", "getCacheConfig", "getQueueConfig", "getLogConfig", "validateConfig", "getEnvVars", "clearCache", "getConfigs", "getPublicConfig", "updateConfig", "batchUpdateConfigs", "resetConfig", "getConfigHistory", "validateConfigValue"], "methods_with_services_data": ["get", "set", "getAll", "getAppConfig", "getDatabaseConfig", "getCacheConfig", "getQueueConfig", "getLogConfig", "validateConfig", "getEnvVars", "clearCache", "getConfigs", "getPublicConfig", "updateConfig", "batchUpdateConfigs", "resetConfig", "getConfigHistory", "validateConfigValue"], "methods_without_services_data": [], "all_public_methods": ["get", "set", "getAll", "getAppConfig", "getDatabaseConfig", "getCacheConfig", "getQueueConfig", "getLogConfig", "validateConfig", "getEnvVars", "clearCache", "getConfigs", "getPublicConfig", "updateConfig", "batchUpdateConfigs", "resetConfig", "getConfigHistory", "validateConfigValue"]}, "DownloadManagementService.php": {"total_public_methods": 1, "constructor_methods": [], "non_constructor_methods": ["secureDownload"], "methods_with_services_data": ["secureDownload"], "methods_without_services_data": [], "all_public_methods": ["secureDownload"]}, "FileService.php": {"total_public_methods": 0, "constructor_methods": [], "non_constructor_methods": [], "methods_with_services_data": [], "methods_without_services_data": [], "all_public_methods": []}, "ImageService.php": {"total_public_methods": 1, "constructor_methods": ["__construct"], "non_constructor_methods": [], "methods_with_services_data": [], "methods_without_services_data": ["__construct"], "all_public_methods": ["__construct"]}, "LogService.php": {"total_public_methods": 13, "constructor_methods": [], "non_constructor_methods": ["writeLog", "getLogs", "readLog", "analyzeLog", "cleanLogs", "downloadLog", "getLogConfig", "getSystemLogs", "getUserActionLogs", "getAiCallLogs", "getErrorLogs", "resolveError", "exportLogs"], "methods_with_services_data": ["writeLog", "getLogs", "readLog", "analyzeLog", "cleanLogs", "downloadLog", "getLogConfig", "getSystemLogs", "getUserActionLogs", "getAiCallLogs", "getErrorLogs", "resolveError", "exportLogs"], "methods_without_services_data": [], "all_public_methods": ["writeLog", "getLogs", "readLog", "analyzeLog", "cleanLogs", "downloadLog", "getLogConfig", "getSystemLogs", "getUserActionLogs", "getAiCallLogs", "getErrorLogs", "resolveError", "exportLogs"]}, "ModelManagementService.php": {"total_public_methods": 0, "constructor_methods": [], "non_constructor_methods": [], "methods_with_services_data": [], "methods_without_services_data": [], "all_public_methods": []}, "ModelService.php": {"total_public_methods": 0, "constructor_methods": [], "non_constructor_methods": [], "methods_with_services_data": [], "methods_without_services_data": [], "all_public_methods": []}, "MusicService.php": {"total_public_methods": 1, "constructor_methods": ["__construct"], "non_constructor_methods": [], "methods_with_services_data": [], "methods_without_services_data": ["__construct"], "all_public_methods": ["__construct"]}, "NotificationService.php": {"total_public_methods": 14, "constructor_methods": [], "non_constructor_methods": ["sendNotification", "sendBulkNotifications", "getNotifications", "getNotification", "resendNotification", "getNotificationStats", "getTemplates", "createTemplate", "getNotificationConfig", "getUserNotifications", "mark<PERSON><PERSON><PERSON>", "markAllAsRead", "deleteNotification", "sendSystemNotification"], "methods_with_services_data": ["sendNotification", "sendBulkNotifications", "getNotifications", "getNotification", "resendNotification", "getNotificationStats", "getTemplates", "createTemplate", "getNotificationConfig", "getUserNotifications", "mark<PERSON><PERSON><PERSON>", "markAllAsRead", "deleteNotification", "sendSystemNotification"], "methods_without_services_data": [], "all_public_methods": ["sendNotification", "sendBulkNotifications", "getNotifications", "getNotification", "resendNotification", "getNotificationStats", "getTemplates", "createTemplate", "getNotificationConfig", "getUserNotifications", "mark<PERSON><PERSON><PERSON>", "markAllAsRead", "deleteNotification", "sendSystemNotification"]}, "PermissionService.php": {"total_public_methods": 17, "constructor_methods": [], "non_constructor_methods": ["checkPermission", "checkMultiplePermissions", "getUserPermissions", "getUserRoles", "getRolePermissions", "assignRole", "removeRole", "getRoles", "getPermissions", "createRole", "updateRolePermissions", "getPermissionStats", "checkResourceAccess", "getUserPermissionDetails", "grantPermissions", "revokePermissions", "getPermissionHistory"], "methods_with_services_data": ["checkPermission", "checkMultiplePermissions", "getUserPermissions", "getUserRoles", "getRolePermissions", "assignRole", "removeRole", "getRoles", "getPermissions", "createRole", "updateRolePermissions", "getPermissionStats", "checkResourceAccess", "getUserPermissionDetails", "grantPermissions", "revokePermissions", "getPermissionHistory"], "methods_without_services_data": [], "all_public_methods": ["checkPermission", "checkMultiplePermissions", "getUserPermissions", "getUserRoles", "getRolePermissions", "assignRole", "removeRole", "getRoles", "getPermissions", "createRole", "updateRolePermissions", "getPermissionStats", "checkResourceAccess", "getUserPermissionDetails", "grantPermissions", "revokePermissions", "getPermissionHistory"]}, "PointsService.php": {"total_public_methods": 0, "constructor_methods": [], "non_constructor_methods": [], "methods_with_services_data": [], "methods_without_services_data": [], "all_public_methods": []}, "PointsTransactionService.php": {"total_public_methods": 6, "constructor_methods": [], "non_constructor_methods": ["createFrozenTransaction", "handleSuccess", "handleFailure", "validateBalance", "getBalance", "getTransactions"], "methods_with_services_data": ["createFrozenTransaction", "handleSuccess", "handleFailure", "validateBalance", "getBalance", "getTransactions"], "methods_without_services_data": [], "all_public_methods": ["createFrozenTransaction", "handleSuccess", "handleFailure", "validateBalance", "getBalance", "getTransactions"]}, "ProjectManagementService.php": {"total_public_methods": 0, "constructor_methods": [], "non_constructor_methods": [], "methods_with_services_data": [], "methods_without_services_data": [], "all_public_methods": []}, "ProjectService.php": {"total_public_methods": 0, "constructor_methods": [], "non_constructor_methods": [], "methods_with_services_data": [], "methods_without_services_data": [], "all_public_methods": []}, "PublicationService.php": {"total_public_methods": 1, "constructor_methods": ["__construct"], "non_constructor_methods": [], "methods_with_services_data": [], "methods_without_services_data": ["__construct"], "all_public_methods": ["__construct"]}, "RecommendationService.php": {"total_public_methods": 0, "constructor_methods": [], "non_constructor_methods": [], "methods_with_services_data": [], "methods_without_services_data": [], "all_public_methods": []}, "ResourceManagementService.php": {"total_public_methods": 1, "constructor_methods": ["__construct"], "non_constructor_methods": [], "methods_with_services_data": [], "methods_without_services_data": ["__construct"], "all_public_methods": ["__construct"]}, "ReviewService.php": {"total_public_methods": 0, "constructor_methods": [], "non_constructor_methods": [], "methods_with_services_data": [], "methods_without_services_data": [], "all_public_methods": []}, "SearchService.php": {"total_public_methods": 0, "constructor_methods": [], "non_constructor_methods": [], "methods_with_services_data": [], "methods_without_services_data": [], "all_public_methods": []}, "SocialService.php": {"total_public_methods": 0, "constructor_methods": [], "non_constructor_methods": [], "methods_with_services_data": [], "methods_without_services_data": [], "all_public_methods": []}, "SoundService.php": {"total_public_methods": 1, "constructor_methods": ["__construct"], "non_constructor_methods": [], "methods_with_services_data": [], "methods_without_services_data": ["__construct"], "all_public_methods": ["__construct"]}, "StoryService.php": {"total_public_methods": 1, "constructor_methods": ["__construct"], "non_constructor_methods": [], "methods_with_services_data": [], "methods_without_services_data": ["__construct"], "all_public_methods": ["__construct"]}, "StyleService.php": {"total_public_methods": 0, "constructor_methods": [], "non_constructor_methods": [], "methods_with_services_data": [], "methods_without_services_data": [], "all_public_methods": []}, "TaskManagementService.php": {"total_public_methods": 1, "constructor_methods": ["__construct"], "non_constructor_methods": [], "methods_with_services_data": [], "methods_without_services_data": ["__construct"], "all_public_methods": ["__construct"]}, "TemplateService.php": {"total_public_methods": 0, "constructor_methods": [], "non_constructor_methods": [], "methods_with_services_data": [], "methods_without_services_data": [], "all_public_methods": []}, "UserGrowthService.php": {"total_public_methods": 0, "constructor_methods": [], "non_constructor_methods": [], "methods_with_services_data": [], "methods_without_services_data": [], "all_public_methods": []}, "UserService.php": {"total_public_methods": 0, "constructor_methods": [], "non_constructor_methods": [], "methods_with_services_data": [], "methods_without_services_data": [], "all_public_methods": []}, "VersionControlService.php": {"total_public_methods": 1, "constructor_methods": ["__construct"], "non_constructor_methods": [], "methods_with_services_data": [], "methods_without_services_data": ["__construct"], "all_public_methods": ["__construct"]}, "VideoService.php": {"total_public_methods": 1, "constructor_methods": ["__construct"], "non_constructor_methods": [], "methods_with_services_data": [], "methods_without_services_data": ["__construct"], "all_public_methods": ["__construct"]}, "VoiceService.php": {"total_public_methods": 1, "constructor_methods": ["__construct"], "non_constructor_methods": [], "methods_with_services_data": [], "methods_without_services_data": ["__construct"], "all_public_methods": ["__construct"]}, "WebSocketEventService.php": {"total_public_methods": 1, "constructor_methods": ["__construct"], "non_constructor_methods": [], "methods_with_services_data": [], "methods_without_services_data": ["__construct"], "all_public_methods": ["__construct"]}, "WebSocketService.php": {"total_public_methods": 0, "constructor_methods": [], "non_constructor_methods": [], "methods_with_services_data": [], "methods_without_services_data": [], "all_public_methods": []}, "WebSocketTokenService.php": {"total_public_methods": 0, "constructor_methods": [], "non_constructor_methods": [], "methods_with_services_data": [], "methods_without_services_data": [], "all_public_methods": []}, "WorkPublishPermissionService.php": {"total_public_methods": 0, "constructor_methods": [], "non_constructor_methods": [], "methods_with_services_data": [], "methods_without_services_data": [], "all_public_methods": []}, "WorkPublishService.php": {"total_public_methods": 1, "constructor_methods": ["__construct"], "non_constructor_methods": [], "methods_with_services_data": [], "methods_without_services_data": ["__construct"], "all_public_methods": ["__construct"]}, "WorkflowService.php": {"total_public_methods": 17, "constructor_methods": [], "non_constructor_methods": ["createWorkflow", "executeWorkflow", "getExecutionStatus", "getWorkflows", "getWorkflow", "updateWorkflow", "deleteWorkflow", "pauseWorkflow", "resumeWorkflow", "getExecutionHistory", "getWorkflowStats", "getWorkflowDetail", "executeWorkflowWithData", "getExecutionStatusWithAuth", "provideStepInput", "cancelExecution", "getExecutionHistoryWithFilters"], "methods_with_services_data": ["createWorkflow", "executeWorkflow", "getExecutionStatus", "getWorkflows", "getWorkflow", "updateWorkflow", "deleteWorkflow", "pauseWorkflow", "resumeWorkflow", "getExecutionHistory", "getWorkflowStats", "getWorkflowDetail", "executeWorkflowWithData", "getExecutionStatusWithAuth", "provideStepInput", "cancelExecution", "getExecutionHistoryWithFilters"], "methods_without_services_data": [], "all_public_methods": ["createWorkflow", "executeWorkflow", "getExecutionStatus", "getWorkflows", "getWorkflow", "updateWorkflow", "deleteWorkflow", "pauseWorkflow", "resumeWorkflow", "getExecutionHistory", "getWorkflowStats", "getWorkflowDetail", "executeWorkflowWithData", "getExecutionStatusWithAuth", "provideStepInput", "cancelExecution", "getExecutionHistoryWithFilters"]}}, "missing_services_data": [{"file": "AiGenerationService.php", "method": "__construct", "is_constructor": true}, {"file": "AiLoadBalancingService.php", "method": "__construct", "is_constructor": true}, {"file": "AiPlatformFallbackService.php", "method": "__construct", "is_constructor": true}, {"file": "AiTaskService.php", "method": "__construct", "is_constructor": true}, {"file": "AudioService.php", "method": "__construct", "is_constructor": true}, {"file": "CharacterService.php", "method": "__construct", "is_constructor": true}, {"file": "ImageService.php", "method": "__construct", "is_constructor": true}, {"file": "MusicService.php", "method": "__construct", "is_constructor": true}, {"file": "PublicationService.php", "method": "__construct", "is_constructor": true}, {"file": "ResourceManagementService.php", "method": "__construct", "is_constructor": true}, {"file": "SoundService.php", "method": "__construct", "is_constructor": true}, {"file": "StoryService.php", "method": "__construct", "is_constructor": true}, {"file": "TaskManagementService.php", "method": "__construct", "is_constructor": true}, {"file": "VersionControlService.php", "method": "__construct", "is_constructor": true}, {"file": "VideoService.php", "method": "__construct", "is_constructor": true}, {"file": "VoiceService.php", "method": "__construct", "is_constructor": true}, {"file": "WebSocketEventService.php", "method": "__construct", "is_constructor": true}, {"file": "WorkPublishService.php", "method": "__construct", "is_constructor": true}]}