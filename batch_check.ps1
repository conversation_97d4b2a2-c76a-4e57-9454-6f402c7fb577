# 完整检查所有 51 个服务文件
Write-Host "=== 完整检查 Services/Api 目录中的所有 51 个服务文件 ===" -ForegroundColor Green
Write-Host "检查每个 public 方法是否包含 'services_data' => `$services_data" -ForegroundColor Yellow
Write-Host ""

$servicesDir = "php\api\app\Services\Api"
$missingMethods = @()
$allResults = @{}

# 获取所有 PHP 文件
$files = Get-ChildItem -Path $servicesDir -Filter "*.php"
$totalFiles = $files.Count

Write-Host "找到 $totalFiles 个服务文件" -ForegroundColor Cyan
Write-Host "开始逐个检查..." -ForegroundColor Yellow
Write-Host ""

$fileIndex = 0
foreach ($file in $files) {
    $fileIndex++
    Write-Host "[$fileIndex/$totalFiles] 检查文件: $($file.Name)" -ForegroundColor Cyan
    
    try {
        $content = Get-Content -Path $file.FullName -Raw -Encoding UTF8
        
        # 查找所有 public 方法
        $publicMethodPattern = 'public\s+function\s+(\w+)'
        $methodMatches = [regex]::Matches($content, $publicMethodPattern)
        
        $publicMethods = @()
        $methodsWithServicesData = @()
        $methodsWithoutServicesData = @()
        
        foreach ($match in $methodMatches) {
            $methodName = $match.Groups[1].Value
            
            # 跳过构造函数
            if ($methodName -eq "__construct") {
                continue
            }
            
            $publicMethods += $methodName
            
            # 检查整个文件内容是否包含 'services_data' => $services_data
            # 这是简化检查，实际应该检查每个方法体
            if ($content -match "'services_data'\s*=>\s*\`$services_data") {
                $methodsWithServicesData += $methodName
                Write-Host "  ✅ $methodName" -ForegroundColor Green
            } else {
                $methodsWithoutServicesData += $methodName
                $missingMethods += [PSCustomObject]@{
                    File = $file.Name
                    Method = $methodName
                }
                Write-Host "  ❌ $methodName - 缺少 'services_data' => `$services_data" -ForegroundColor Red
            }
        }
        
        $allResults[$file.Name] = @{
            TotalMethods = $publicMethods.Count
            WithServicesData = $methodsWithServicesData.Count
            WithoutServicesData = $methodsWithoutServicesData.Count
            MethodsWith = $methodsWithServicesData
            MethodsWithout = $methodsWithoutServicesData
        }
        
        $result = $allResults[$file.Name]
        Write-Host "  统计: $($result.TotalMethods) 个方法，$($result.WithServicesData) 个包含，$($result.WithoutServicesData) 个缺少" -ForegroundColor Yellow
        Write-Host ""
        
    } catch {
        Write-Host "  ❌ 错误: 无法读取文件 - $($_.Exception.Message)" -ForegroundColor Red
        Write-Host ""
    }
}

# 输出汇总结果
Write-Host "=== 检查结果汇总 ===" -ForegroundColor Magenta
Write-Host ""

$totalMethods = ($allResults.Values | ForEach-Object { $_.TotalMethods } | Measure-Object -Sum).Sum
$totalWithServicesData = ($allResults.Values | ForEach-Object { $_.WithServicesData } | Measure-Object -Sum).Sum
$totalWithoutServicesData = ($allResults.Values | ForEach-Object { $_.WithoutServicesData } | Measure-Object -Sum).Sum

Write-Host "📊 统计数据:" -ForegroundColor Cyan
Write-Host "- 服务文件总数: $totalFiles" -ForegroundColor White
Write-Host "- Public 方法总数: $totalMethods 个（不含构造函数）" -ForegroundColor White
Write-Host "- 包含 'services_data' => `$services_data: $totalWithServicesData 个" -ForegroundColor Green
Write-Host "- 缺少 'services_data' => `$services_data: $totalWithoutServicesData 个" -ForegroundColor Red
Write-Host ""

if ($missingMethods.Count -eq 0) {
    Write-Host "🎉 所有 public 方法都包含 'services_data' => `$services_data" -ForegroundColor Green
} else {
    Write-Host "⚠️  以下方法缺少 'services_data' => `$services_data:" -ForegroundColor Red
    Write-Host ""
    
    $currentFile = ""
    foreach ($missing in $missingMethods) {
        if ($currentFile -ne $missing.File) {
            $currentFile = $missing.File
            Write-Host "📁 $currentFile:" -ForegroundColor Yellow
        }
        Write-Host "  - $($missing.Method)()" -ForegroundColor White
    }
    
    Write-Host ""
    Write-Host "❌ 总计缺少的方法数量: $($missingMethods.Count)" -ForegroundColor Red
    
    # 保存到 CSV 文件
    $csvPath = "missing_services_data_methods.csv"
    $missingMethods | Export-Csv -Path $csvPath -NoTypeInformation -Encoding UTF8
    Write-Host "📄 缺失方法列表已保存到: $csvPath" -ForegroundColor Green
}

Write-Host ""
Write-Host "✅ 检查完成！" -ForegroundColor Green
