Write-Host "开始扫描 Services/Api 目录..." -ForegroundColor Green

$servicesDir = "php\api\app\Services\Api"
$missingMethods = @()

if (-not (Test-Path $servicesDir)) {
    Write-Host "错误: 目录不存在 - $servicesDir" -ForegroundColor Red
    exit 1
}

$files = Get-ChildItem -Path $servicesDir -Filter "*.php"
Write-Host "找到 $($files.Count) 个服务文件"
Write-Host ""

foreach ($file in $files) {
    Write-Host "检查文件: $($file.Name)" -ForegroundColor Cyan
    
    $content = Get-Content -Path $file.FullName -Raw -Encoding UTF8
    
    # 查找所有 public 方法
    $publicMethodPattern = 'public\s+function\s+(\w+)'
    $methodMatches = [regex]::Matches($content, $publicMethodPattern)
    
    $methodCount = 0
    $missingCount = 0
    
    foreach ($match in $methodMatches) {
        $methodName = $match.Groups[1].Value
        
        # 跳过构造函数
        if ($methodName -eq "__construct") {
            continue
        }
        
        $methodCount++
        
        # 检查整个文件是否包含 'services_data' => $services_data
        # 这是一个简化的检查，实际应该检查每个方法体
        if ($content -notmatch "'services_data'\s*=>\s*\`$services_data") {
            $missingMethods += [PSCustomObject]@{
                File = $file.Name
                Method = $methodName
            }
            $missingCount++
            Write-Host "  ❌ $methodName - 可能缺少 'services_data' => `$services_data" -ForegroundColor Red
        } else {
            Write-Host "  ✅ $methodName - 文件包含 'services_data' => `$services_data" -ForegroundColor Green
        }
    }
    
    Write-Host "  方法统计: 总计 $methodCount 个非构造函数方法" -ForegroundColor Yellow
    Write-Host ""
}

# 输出汇总结果
Write-Host "=== 检查结果汇总 ===" -ForegroundColor Magenta
Write-Host ""

if ($missingMethods.Count -eq 0) {
    Write-Host "✅ 所有文件都包含 'services_data' => `$services_data" -ForegroundColor Green
} else {
    Write-Host "❌ 以下方法可能缺少 'services_data' => `$services_data:" -ForegroundColor Red
    Write-Host ""
    
    $currentFile = ""
    foreach ($missing in $missingMethods) {
        if ($currentFile -ne $missing.File) {
            $currentFile = $missing.File
            Write-Host "文件: $currentFile" -ForegroundColor Yellow
        }
        Write-Host "  - $($missing.Method)()" -ForegroundColor White
    }
    
    Write-Host ""
    Write-Host "总计可能缺少的方法数量: $($missingMethods.Count)" -ForegroundColor Red
    
    # 保存到 CSV 文件
    $csvPath = "missing_services_data_methods.csv"
    $missingMethods | Export-Csv -Path $csvPath -NoTypeInformation -Encoding UTF8
    Write-Host "结果已保存到: $csvPath" -ForegroundColor Green
}

Write-Host ""
Write-Host "检查完成！" -ForegroundColor Green
