<?php

echo "=== 100% 完整检查所有 51 个服务文件 ===\n";
echo "逐个检查每个文件的每个 public 方法\n\n";

$servicesDir = 'php/api/app/Services/Api';
$missingMethods = [];
$allResults = [];
$totalMethodsChecked = 0;
$totalMethodsWithServicesData = 0;
$totalMethodsWithoutServicesData = 0;

// 获取所有文件列表
$allFiles = [
    'AdService.php',
    'AiGenerationService.php', 
    'AiLoadBalancingService.php',
    'AiModelService.php',
    'AiPlatformFallbackService.php',
    'AiPlatformHealthService.php',
    'AiPlatformSelectionService.php',
    'AiTaskService.php',
    'AnalyticsService.php',
    'AssetService.php',
    'AudioService.php',
    'AuthService.php',
    'BatchService.php',
    'CacheService.php',
    'CharacterService.php',
    'ConfigService.php',
    'DownloadManagementService.php',
    'FileService.php',
    'ImageService.php',
    'LogService.php',
    'ModelManagementService.php',
    'ModelService.php',
    'MusicService.php',
    'NotificationService.php',
    'PermissionService.php',
    'PointsService.php',
    'PointsTransactionService.php',
    'ProjectManagementService.php',
    'ProjectService.php',
    'PublicationService.php',
    'RecommendationService.php',
    'ResourceManagementService.php',
    'ReviewService.php',
    'SearchService.php',
    'SocialService.php',
    'SoundService.php',
    'StoryService.php',
    'StyleService.php',
    'TaskManagementService.php',
    'TemplateService.php',
    'UserGrowthService.php',
    'UserService.php',
    'VersionControlService.php',
    'VideoService.php',
    'VoiceService.php',
    'WebSocketEventService.php',
    'WebSocketService.php',
    'WebSocketTokenService.php',
    'WorkflowService.php',
    'WorkPublishPermissionService.php',
    'WorkPublishService.php'
];

echo "确认文件总数: " . count($allFiles) . " 个\n\n";

$fileIndex = 0;
foreach ($allFiles as $fileName) {
    $fileIndex++;
    $filePath = $servicesDir . '/' . $fileName;
    
    echo "[{$fileIndex}/51] 检查文件: {$fileName}\n";
    
    if (!file_exists($filePath)) {
        echo "  ❌ 文件不存在: {$filePath}\n\n";
        continue;
    }
    
    $content = file_get_contents($filePath);
    if ($content === false) {
        echo "  ❌ 无法读取文件\n\n";
        continue;
    }
    
    // 查找所有 public 方法
    preg_match_all('/public\s+function\s+(\w+)\s*\([^)]*\)\s*\{/', $content, $matches, PREG_OFFSET_CAPTURE);
    
    $publicMethods = [];
    $methodsWithServicesData = [];
    $methodsWithoutServicesData = [];
    
    foreach ($matches[1] as $index => $match) {
        $methodName = $match[0];
        $methodStartPos = $matches[0][$index][1];
        
        // 跳过构造函数
        if ($methodName === '__construct') {
            continue;
        }
        
        $publicMethods[] = $methodName;
        $totalMethodsChecked++;
        
        // 提取方法体
        $methodBody = extractMethodBody($content, $methodStartPos);
        
        // 检查是否包含 'services_data' => $services_data
        if (strpos($methodBody, "'services_data' => \$services_data") !== false) {
            $methodsWithServicesData[] = $methodName;
            $totalMethodsWithServicesData++;
            echo "  ✅ {$methodName}\n";
        } else {
            $methodsWithoutServicesData[] = $methodName;
            $totalMethodsWithoutServicesData++;
            $missingMethods[] = [
                'file' => $fileName,
                'method' => $methodName
            ];
            echo "  ❌ {$methodName} - 缺少 'services_data' => \$services_data\n";
        }
    }
    
    $allResults[$fileName] = [
        'total_methods' => count($publicMethods),
        'with_services_data' => count($methodsWithServicesData),
        'without_services_data' => count($methodsWithoutServicesData),
        'methods_with' => $methodsWithServicesData,
        'methods_without' => $methodsWithoutServicesData
    ];
    
    echo "  统计: " . count($publicMethods) . " 个方法，";
    echo count($methodsWithServicesData) . " 个包含，";
    echo count($methodsWithoutServicesData) . " 个缺少\n\n";
}

// 输出最终汇总
echo "=== 100% 完整检查结果汇总 ===\n\n";

echo "📊 最终统计数据:\n";
echo "- 服务文件总数: 51\n";
echo "- Public 方法总数: {$totalMethodsChecked} 个（不含构造函数）\n";
echo "- 包含 'services_data' => \$services_data: {$totalMethodsWithServicesData} 个\n";
echo "- 缺少 'services_data' => \$services_data: {$totalMethodsWithoutServicesData} 个\n";
echo "- 符合率: " . round(($totalMethodsWithServicesData / $totalMethodsChecked) * 100, 2) . "%\n\n";

if (empty($missingMethods)) {
    echo "🎉 所有 public 方法都包含 'services_data' => \$services_data\n";
} else {
    echo "⚠️  以下方法缺少 'services_data' => \$services_data:\n\n";
    
    $currentFile = '';
    foreach ($missingMethods as $missing) {
        if ($currentFile !== $missing['file']) {
            $currentFile = $missing['file'];
            echo "📁 {$currentFile}:\n";
        }
        echo "  - {$missing['method']}()\n";
    }
    
    echo "\n❌ 总计缺少的方法数量: " . count($missingMethods) . "\n";
}

// 保存详细结果
$csvContent = "服务文件名,方法名\n";
foreach ($missingMethods as $missing) {
    $csvContent .= "\"{$missing['file']}\",\"{$missing['method']}\"\n";
}
file_put_contents('complete_missing_methods.csv', $csvContent);

$reportData = [
    'scan_time' => date('Y-m-d H:i:s'),
    'total_files' => 51,
    'total_methods' => $totalMethodsChecked,
    'methods_with_services_data' => $totalMethodsWithServicesData,
    'methods_without_services_data' => $totalMethodsWithoutServicesData,
    'compliance_rate' => round(($totalMethodsWithServicesData / $totalMethodsChecked) * 100, 2),
    'file_details' => $allResults,
    'missing_methods' => $missingMethods
];

file_put_contents('complete_check_report.json', json_encode($reportData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

echo "📄 完整报告已保存到: complete_check_report.json\n";
echo "📄 缺失方法列表已保存到: complete_missing_methods.csv\n";
echo "\n✅ 100% 完整检查完成！\n";

/**
 * 提取方法体内容
 */
function extractMethodBody($content, $startPos)
{
    $braceCount = 0;
    $inMethod = false;
    $methodBody = '';
    $length = strlen($content);
    
    for ($i = $startPos; $i < $length; $i++) {
        $char = $content[$i];
        
        if ($char === '{') {
            $braceCount++;
            $inMethod = true;
        } elseif ($char === '}') {
            $braceCount--;
        }
        
        if ($inMethod) {
            $methodBody .= $char;
        }
        
        if ($inMethod && $braceCount === 0) {
            break;
        }
    }
    
    return $methodBody;
}

?>
