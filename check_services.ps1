# PowerShell 脚本：检查 Services/Api 目录中的所有服务文件
# 查找每个 public 方法是否包含 'services_data' => $services_data

# 提取方法体的函数
function Extract-MethodBody {
    param(
        [string]$Content,
        [int]$StartPos
    )

    $braceCount = 0
    $inMethod = $false
    $methodBody = ""
    $length = $Content.Length

    for ($i = $StartPos; $i -lt $length; $i++) {
        $char = $Content[$i]

        if ($char -eq '{') {
            $braceCount++
            $inMethod = $true
        } elseif ($char -eq '}') {
            $braceCount--
        }

        if ($inMethod) {
            $methodBody += $char
        }

        if ($inMethod -and $braceCount -eq 0) {
            break
        }
    }

    return $methodBody
}

Write-Host "开始扫描 Services/Api 目录..." -ForegroundColor Green
Write-Host ""

$servicesDir = "php\api\app\Services\Api"
$missingMethods = @()

# 检查目录是否存在
if (-not (Test-Path $servicesDir)) {
    Write-Host "错误: 目录不存在 - $servicesDir" -ForegroundColor Red
    exit 1
}

# 获取所有 PHP 文件
$files = Get-ChildItem -Path $servicesDir -Filter "*.php"
Write-Host "找到 $($files.Count) 个服务文件" -ForegroundColor Yellow
Write-Host ""

foreach ($file in $files) {
    Write-Host "检查文件: $($file.Name)" -ForegroundColor Cyan
    
    try {
        $content = Get-Content -Path $file.FullName -Raw -Encoding UTF8
        
        # 使用正则表达式查找所有 public 方法
        $publicMethodPattern = 'public\s+function\s+(\w+)\s*\([^)]*\)\s*\{'
        $methodMatches = [regex]::Matches($content, $publicMethodPattern)
        
        $methodCount = 0
        $missingCount = 0
        
        foreach ($match in $methodMatches) {
            $methodName = $match.Groups[1].Value
            
            # 跳过构造函数
            if ($methodName -eq "__construct") {
                continue
            }
            
            $methodCount++
            
            # 提取方法体
            $methodStartPos = $match.Index
            $methodBody = Extract-MethodBody -Content $content -StartPos $methodStartPos
            
            # 检查是否包含 'services_data' => $services_data
            if ($methodBody -notmatch "'services_data'\s*=>\s*\`$services_data") {
                $missingMethods += [PSCustomObject]@{
                    File = $file.Name
                    Method = $methodName
                }
                $missingCount++
                Write-Host "  ❌ $methodName - 缺少 'services_data' => `$services_data" -ForegroundColor Red
            } else {
                Write-Host "  ✅ $methodName - 包含 'services_data' => `$services_data" -ForegroundColor Green
            }
        }
        
        Write-Host "  方法统计: 总计 $methodCount 个，缺少 $missingCount 个" -ForegroundColor Yellow
        Write-Host ""
        
    } catch {
        Write-Host "  错误: 无法读取文件 - $($_.Exception.Message)" -ForegroundColor Red
        Write-Host ""
    }
}

# 输出汇总结果
Write-Host "=== 检查结果汇总 ===" -ForegroundColor Magenta
Write-Host ""

if ($missingMethods.Count -eq 0) {
    Write-Host "✅ 所有 public 方法都包含 'services_data' => `$services_data" -ForegroundColor Green
} else {
    Write-Host "❌ 以下方法缺少 'services_data' => `$services_data:" -ForegroundColor Red
    Write-Host ""
    
    $currentFile = ""
    foreach ($missing in $missingMethods) {
        if ($currentFile -ne $missing.File) {
            $currentFile = $missing.File
            Write-Host "文件: $currentFile" -ForegroundColor Yellow
        }
        Write-Host "  - $($missing.Method)()" -ForegroundColor White
    }
    
    Write-Host ""
    Write-Host "总计缺少的方法数量: $($missingMethods.Count)" -ForegroundColor Red
    
    # 保存到 CSV 文件
    $csvPath = "missing_services_data_methods.csv"
    $missingMethods | Export-Csv -Path $csvPath -NoTypeInformation -Encoding UTF8
    Write-Host "结果已保存到: $csvPath" -ForegroundColor Green
}

Write-Host ""
Write-Host "检查完成！" -ForegroundColor Green
