<?php

echo "开始扫描 Services/Api 目录...\n\n";

$servicesDir = 'php/api/app/Services/Api';
$missingMethods = [];

// 检查目录是否存在
if (!is_dir($servicesDir)) {
    echo "错误: 目录不存在 - {$servicesDir}\n";
    exit(1);
}

// 获取所有 PHP 文件
$files = glob($servicesDir . '/*.php');
echo "找到 " . count($files) . " 个服务文件\n\n";

// 添加调试信息
echo "目录路径: " . realpath($servicesDir) . "\n";
echo "文件列表:\n";
foreach ($files as $file) {
    echo "  - " . basename($file) . "\n";
}
echo "\n";

foreach ($files as $file) {
    $fileName = basename($file);
    echo "检查文件: {$fileName}\n";
    
    $content = file_get_contents($file);
    if ($content === false) {
        echo "  错误: 无法读取文件\n";
        continue;
    }
    
    // 查找所有 public 方法
    preg_match_all('/public\s+function\s+(\w+)\s*\([^)]*\)\s*\{/', $content, $matches, PREG_OFFSET_CAPTURE);
    
    $methodCount = 0;
    $missingCount = 0;
    
    foreach ($matches[1] as $index => $match) {
        $methodName = $match[0];
        $methodStartPos = $matches[0][$index][1];
        
        // 跳过构造函数
        if ($methodName === '__construct') {
            continue;
        }
        
        $methodCount++;
        
        // 提取方法体
        $methodBody = extractMethodBody($content, $methodStartPos);
        
        // 检查是否包含 'services_data' => $services_data
        if (strpos($methodBody, "'services_data' => \$services_data") === false) {
            $missingMethods[] = [
                'file' => $fileName,
                'method' => $methodName
            ];
            $missingCount++;
            echo "  ❌ {$methodName} - 缺少 'services_data' => \$services_data\n";
        } else {
            echo "  ✅ {$methodName} - 包含 'services_data' => \$services_data\n";
        }
    }
    
    echo "  方法统计: 总计 {$methodCount} 个，缺少 {$missingCount} 个\n\n";
}

// 输出汇总结果
echo "=== 检查结果汇总 ===\n\n";

if (empty($missingMethods)) {
    echo "✅ 所有 public 方法都包含 'services_data' => \$services_data\n";
} else {
    echo "❌ 以下方法缺少 'services_data' => \$services_data:\n\n";
    
    $currentFile = '';
    foreach ($missingMethods as $missing) {
        if ($currentFile !== $missing['file']) {
            $currentFile = $missing['file'];
            echo "文件: {$currentFile}\n";
        }
        echo "  - {$missing['method']}()\n";
    }
    
    echo "\n总计缺少的方法数量: " . count($missingMethods) . "\n";
    
    // 保存到文件
    $csvContent = "服务文件名,方法名\n";
    foreach ($missingMethods as $missing) {
        $csvContent .= "{$missing['file']},{$missing['method']}\n";
    }
    file_put_contents('missing_services_data_methods.csv', $csvContent);
    echo "结果已保存到: missing_services_data_methods.csv\n";
}

/**
 * 提取方法体内容
 */
function extractMethodBody($content, $startPos)
{
    $braceCount = 0;
    $inMethod = false;
    $methodBody = '';
    $length = strlen($content);
    
    for ($i = $startPos; $i < $length; $i++) {
        $char = $content[$i];
        
        if ($char === '{') {
            $braceCount++;
            $inMethod = true;
        } elseif ($char === '}') {
            $braceCount--;
        }
        
        if ($inMethod) {
            $methodBody .= $char;
        }
        
        if ($inMethod && $braceCount === 0) {
            break;
        }
    }
    
    return $methodBody;
}

echo "\n检查完成！\n";

?>
